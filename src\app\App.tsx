import { MantineProvider } from "@mantine/core";
import { createHashH<PERSON><PERSON>, createRouter, RouterProvider } from "@tanstack/react-router";
import { homeRoute, localGalleryRoute, rootRoute } from "app/routes";
import { store } from "app/store/store";
import { theme } from "app/theme";
import { createRoot } from "react-dom/client";
import { Provider } from "react-redux";

const routeTree = rootRoute.addChildren([homeRoute, localGalleryRoute]);
const history = createHashHistory();
const router = createRouter({ routeTree, history, context: { navbar: null, footer: null } });

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

const root = createRoot(document.getElementById("root") as HTMLDivElement);
root.render(
  <MantineProvider theme={theme} forceColorScheme="dark">
    <Provider store={store}>
      <RouterProvider router={router} />
    </Provider>
  </MantineProvider>
);
