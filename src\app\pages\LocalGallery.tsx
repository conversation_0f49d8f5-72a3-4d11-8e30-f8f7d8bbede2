import { Lo<PERSON>, ScrollArea, SimpleGrid } from "@mantine/core";
import { Thumbnail } from "app/components/Thumbnail/Thumbnail";
import { useAppDispatch, useAppSelector } from "app/hooks";
import { fetchManyThunk } from "app/store/thunks/localGalleryThunk";
import { forwardRef, useCallback, useEffect } from "react";
import useInfiniteScroll from "react-infinite-scroll-hook";
import { FetchStatus } from "src/shared/constants";
import { content, loading, simpleGrid, viewport } from "./LocalGallery.css";

export function LocalGallery() {
  const dispatch = useAppDispatch();
  const fetchMany = useAppSelector((store) => store.localGallery.fetchMany);

  const isLoading = fetchMany.status === FetchStatus.LOADING;
  const hasNextPage = fetchMany.hasNextPage;
  const showLoadingIndicator = hasNextPage || isLoading;

  const onLoadMore = useCallback(() => {
    dispatch(fetchManyThunk({ page: fetchMany.page + 1 }));
  }, [dispatch, fetchMany.page]);

  const [infiniteRef] = useInfiniteScroll({ loading: isLoading, hasNextPage, onLoadMore });

  // Initial fetch on mount
  useEffect(() => {
    if (fetchMany.status === FetchStatus.IDLE) {
      dispatch(fetchManyThunk({ page: 1 }));
    }
  }, [dispatch, fetchMany.status]);

  return (
    <ScrollArea.Autosize scrollbars="y" scrollbarSize={20} classNames={{ content, viewport }}>
      <SimpleGrid cols={{ base: 1, sm: 1, md: 2, lg: 3, xl: 4 }} className={simpleGrid}>
        {fetchMany.data.map((image) => (
          <Thumbnail key={image.id} image={image} />
        ))}
        {showLoadingIndicator && <LoadingIndicator ref={infiniteRef} />}
      </SimpleGrid>
    </ScrollArea.Autosize>
  );
}

const LoadingIndicator = forwardRef<HTMLDivElement>((props, ref) => {
  return (
    <div ref={ref} className={loading}>
      <Loader />
    </div>
  );
});
