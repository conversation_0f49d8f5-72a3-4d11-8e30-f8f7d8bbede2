import { useEffect, useState } from "react";

export function useDevtools() {
  const [state, setState] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    window.electronDevtools.onOpened(() => {
      setState(true);
      setLoading(false);
    });
    window.electronDevtools.onClosed(() => {
      setState(false);
      setLoading(false);
    });

    return () => {
      window.electronDevtools.removeListeners();
    };
  }, []);

  return {
    state,
    loading,
    toggle: async () => {
      setLoading(true);
      await window.electronDevtools.toggle();
    },
  };
}
