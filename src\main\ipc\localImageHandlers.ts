import { ipcMain } from "electron";
import { LOCAL_IMAGES } from "shared/constants/ipc-channels";
import type { LocalImageFindManyOptions } from "shared/types/localImageTypes";
import { LocalImageService } from "src/main/db/localImageService";

const service = new LocalImageService();

export function registerLocalImageHandlers() {
  ipcMain.handle(LOCAL_IMAGES.SYNC, async () => {
    try {
      return await service.sync();
    } catch (error) {
      console.error("Error syncing local images:", error);
      throw error;
    }
  });

  ipcMain.handle(LOCAL_IMAGES.FIND_MANY, async (event, options: LocalImageFindManyOptions) => {
    try {
      return await service.fetchMany(options);
    } catch (error) {
      console.error("Error fetching local images:", error);
      throw error;
    }
  });
}
