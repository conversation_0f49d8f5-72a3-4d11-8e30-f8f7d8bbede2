import { style } from "@vanilla-extract/css";
import { vars } from "app/styles/vars.css";
import { mantineVars } from "app/theme";

export const thumbnail = style({
  position: "relative",
  borderRadius: mantineVars.radius.md,
  height: vars.thumbnailHeight,
  transition: "transform 180ms ease",
  backgroundColor: mantineVars.colors.black,
  backgroundPosition: "center",
  backgroundRepeat: "no-repeat",
  backgroundSize: "cover",

  selectors: {
    "&:hover": { transform: "scale(1.05)" },
  },
});

export const thumbnailGroup = style({
  justifyContent: "space-between",
  bottom: 0,
  left: 0,
  position: "absolute",
  padding: mantineVars.spacing.sm,
  width: "100%",
  transform: "translateY(20px)",
  transition: "opacity 180ms ease-in-out, transform 180ms ease-out",
  backdropFilter: "blur(3px)",
  background: "rgba(0, 0, 0, 0.4)",
  opacity: 0,
  pointerEvents: "none",

  selectors: {
    [`${thumbnail}:hover &`]: {
      transform: "translateY(0)",
      opacity: 1,
      pointerEvents: "auto",
    },
  },
});

export const thumbnailButton = style({
  borderRadius: mantineVars.radius.md,

  vars: {
    "--ai-size": "54px",
  },
});
