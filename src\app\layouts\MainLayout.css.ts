import { globalStyle, style } from "@vanilla-extract/css";
import { vars } from "app/styles/vars.css";

export const layout = style({
  backgroundColor: vars.bgColorPrimary,

  vars: {
    "--mantine-color-body": vars.bgColorPrimary,
  },
});

globalStyle(`${layout} > header`, {
  zIndex: "var(--mantine-z-index-max)",
  backgroundColor: vars.bgColorSecondary,
});

globalStyle(`${layout} > main`, {});

globalStyle(`${layout} > nav`, {});

globalStyle(`${layout} > footer`, {});
