import { type BrowserWindow, ipc<PERSON>ain, shell } from "electron";
import { WINDOW } from "shared/constants/ipc-channels";

export function registerWindowHandlers(window: BrowserWindow) {
  ipcMain.handle(WINDOW.MINIMIZE, () => window.minimize());
  ipcMain.handle(WINDOW.MAXIMIZE, () => {
    if (window.isMaximized()) {
      window.unmaximize();
    } else {
      window.maximize();
    }
  });
  ipcMain.handle(WINDOW.CLOSE, () => window.close());
  ipcMain.handle(WINDOW.OPEN_PATH, (event, path: string) => shell.openPath(path));
  ipcMain.handle(WINDOW.FORCE_RELOAD, () => window.webContents.reloadIgnoringCache());
}
