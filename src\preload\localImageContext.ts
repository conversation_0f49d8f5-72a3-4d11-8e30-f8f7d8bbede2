import { ipc<PERSON><PERSON><PERSON> } from "electron";
import { LOCAL_IMAGES } from "shared/constants/ipc-channels";
import type {
  LocalImageFindManyOptions,
  LocalImageFindManyResponse,
  LocalImageSyncResponse,
} from "shared/types/localImageTypes";

export const localImageContext = {
  sync: (): Promise<LocalImageSyncResponse> => {
    return ipcRenderer.invoke(LOCAL_IMAGES.SYNC);
  },
  findMany: (options: LocalImageFindManyOptions): Promise<LocalImageFindManyResponse> => {
    return ipcRenderer.invoke(LOCAL_IMAGES.FIND_MANY, options);
  },
};
