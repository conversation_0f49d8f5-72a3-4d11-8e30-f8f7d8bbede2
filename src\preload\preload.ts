import { contextBridge } from "electron";
import { devtoolsContext } from "./devtoolsContext";
import { localImageContext } from "./localImageContext";
import { windowContext } from "./windowContext";

function exposeAllContexts() {
  contextBridge.exposeInMainWorld("electronWindow", windowContext);
  contextBridge.exposeInMainWorld("electronDevtools", devtoolsContext);
  contextBridge.exposeInMainWorld("electronLocalImages", localImageContext);
}

exposeAllContexts();
