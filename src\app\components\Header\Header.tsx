import type { UnstyledButtonProps } from "@mantine/core";
import { Avatar, Box, Divider, Group, UnstyledButton } from "@mantine/core";
import type { LinkComponent } from "@tanstack/react-router";
import { createLink } from "@tanstack/react-router";
import { assignInlineVars } from "@vanilla-extract/dynamic";
import { HeaderButton, bgColorHover } from "app/components/HeaderButton/HeaderButton";
import { HeaderMenu } from "app/components/HeaderMenu/HeaderMenu";
import { forwardRef } from "react";
import { mantineVars } from "src/app/theme";
import { classes } from "./Header.css";

const logoSrc = new URL("src/assets/logo.png", import.meta.url).toString();

export function Header() {
  return (
    <Box className={classes.header}>
      <Group className={classes.headerLeft}>
        <Avatar src={logoSrc} size={30} classNames={{ root: classes.headerLogo }} />
        <HeaderLink to="/">Home</HeaderLink>
        <HeaderLink to="/local-gallery">Local Gallery</HeaderLink>
      </Group>

      <Group justify="center">{/* TODO */}</Group>

      <Group className={classes.headerRight}>
        <HeaderMenu />
        <Divider size="xs" orientation="vertical" />
        <HeaderButton icon="minimize" onClick={() => window.electronWindow.minimize()} />
        <HeaderButton icon="maximize" onClick={() => window.electronWindow.maximize()} />
        <HeaderButton
          icon="close"
          style={assignInlineVars({ [bgColorHover]: mantineVars.colors.red[9] })}
          onClick={() => window.electronWindow.close()}
        />
      </Group>
    </Box>
  );
}

const Button = forwardRef<HTMLButtonElement, UnstyledButtonProps>((props, ref) => (
  <UnstyledButton ref={ref} {...props} />
));

const CreatedLinkComponent = createLink(Button);

const HeaderLink: LinkComponent<typeof Button> = (props) => {
  return (
    <CreatedLinkComponent
      preload="intent"
      className={classes.headerLink["inactive"]}
      activeProps={{ className: classes.headerLink["active"] }}
      {...props}
    />
  );
};
