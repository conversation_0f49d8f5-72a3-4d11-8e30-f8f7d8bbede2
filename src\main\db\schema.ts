import { sql } from "drizzle-orm";
import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";

export const LocalImagesTable = sqliteTable("local_images_table", {
  id: integer().primary<PERSON>ey({ autoIncrement: true }),
  filePath: text().notNull(),
  fileName: text().notNull(),
  fileSize: integer().notNull(),
  thumbnailPath: text().notNull(),
  width: integer().notNull(),
  height: integer().notNull(),
  createdAt: text()
    .notNull()
    .default(sql`(current_timestamp)`),
  isArchived: integer({ mode: "boolean" }).notNull().default(false),
  isPortrait: integer({ mode: "boolean" }).notNull().default(false),
});

export type LocalImage = typeof LocalImagesTable.$inferSelect;
