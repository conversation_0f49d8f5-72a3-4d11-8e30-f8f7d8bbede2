import { rem } from "@mantine/core";
import { createGlobalTheme, createGlobalThemeContract, globalStyle } from "@vanilla-extract/css";

export const vars = createGlobalThemeContract({
  footerHeight: "footer-height",
  headerHeight: "header-height",
  navbarWidth: "navbar-width",

  // SIZE
  thumbnailHeight: "image-card-height",

  // COLORS
  bgColorPrimary: "bg-color-primary",
  bgColorSecondary: "bg-color-secondary",
});

createGlobalTheme(":root", vars, {
  footerHeight: rem(48),
  headerHeight: rem(48),
  navbarWidth: rem(300),

  // SIZE
  thumbnailHeight: rem(450),

  // COLORS
  bgColorPrimary: "#1D1E30",
  bgColorSecondary: "#0C0D21",
});

globalStyle("body", {
  backgroundColor: vars.bgColorPrimary,
  overflow: "hidden",
});
