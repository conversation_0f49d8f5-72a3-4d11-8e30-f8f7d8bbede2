import { createVar, style, styleVariants } from "@vanilla-extract/css";
import { calc } from "@vanilla-extract/css-utils";
import { mantineVars } from "src/app/theme";

const bgColorVar = createVar();

const header = style({
  alignItems: "center",
  display: "grid",
  gridTemplateColumns: "1fr auto 1fr",
  height: "100%",
  paddingLeft: mantineVars.spacing.xs,

  "-webkit-app-region": "drag",
});

const headerLogo = style({
  marginInline: calc.divide(mantineVars.spacing.xs, 2),
  filter: "brightness(50%)",
});

const headerLeft = style({
  flexWrap: "nowrap",
  gap: 0,
  height: "100%",
});

const headerRight = style({
  flexWrap: "nowrap",
  gap: 0,
  justifySelf: "end",
  height: "100%",

  "-webkit-app-region": "no-drag",
});

const headerLinkBase = style({
  alignItems: "center",
  display: "flex",
  height: "100%",
  paddingInline: mantineVars.spacing.xs,
  fontSize: mantineVars.fontSizes.xs,
  letterSpacing: "0.1em",
  textTransform: "uppercase",
  textWrap: "nowrap",

  "-webkit-app-region": "no-drag",
  ":active": { transform: "translate(1px, 1px)" },
  ":focus": { outline: "none" },
  ":hover": {
    textDecoration: "underline",
    textDecorationColor: mantineVars.colors.blue[7],
    textDecorationStyle: "double",
    textDecorationThickness: "1px",
    textUnderlineOffset: "5px",
  },
});

const headerLink = styleVariants({
  active: [
    headerLinkBase,
    {
      textDecoration: "underline",
      textDecorationColor: mantineVars.colors.blue[7],
      textDecorationStyle: "double",
      textDecorationThickness: "1px",
      textUnderlineOffset: "5px",
    },
  ],
  inactive: [headerLinkBase],
});

export const classes = {
  header,
  headerLeft,
  headerLink,
  headerLogo,
  headerRight,
};

export { bgColorVar };
