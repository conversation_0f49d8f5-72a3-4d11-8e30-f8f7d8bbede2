import { ipc<PERSON><PERSON><PERSON> } from "electron";
import { WINDOW } from "shared/constants/ipc-channels";

export const windowContext = {
  openPath: (path: string): Promise<void> => ipcRenderer.invoke(WINDOW.OPEN_PATH, path),
  forceReload: (): Promise<void> => ipcRenderer.invoke(WINDOW.FORCE_RELOAD),
  minimize: (): Promise<void> => ipcRenderer.invoke(WINDOW.MINIMIZE),
  maximize: (): Promise<void> => ipcRenderer.invoke(WINDOW.MAXIMIZE),
  close: (): Promise<void> => ipcRenderer.invoke(WINDOW.CLOSE),
};
