import { UnstyledButton } from "@mantine/core";
import { MenuIcon, Minus, Square, X } from "lucide-react";
import { forwardRef } from "react";
import { headerButton, headerIcon } from "./HeaderButton.css";

const iconMap = { menu: MenuIcon, minimize: Minus, maximize: Square, close: X };

interface HeaderButtonProps extends React.ComponentPropsWithoutRef<"button"> {
  icon: keyof typeof iconMap;
}

export const HeaderButton = forwardRef<HTMLButtonElement, HeaderButtonProps>(function HeaderButton(props, ref) {
  const { icon, ...rest } = props;
  const Icon = iconMap[icon];

  return (
    <UnstyledButton ref={ref} tabIndex={-1} {...rest} className={headerButton}>
      <Icon
        absoluteStrokeWidth={true}
        className={headerIcon}
        style={icon === "maximize" ? { transform: "scale(0.7)" } : {}}
      />
    </UnstyledButton>
  );
});

export { bgColorHover } from "./HeaderButton.css";
