{"workbench.tree.indent": 18, "workbench.tree.renderIndentGuides": "always", "terminal.integrated.defaultProfile.osx": "zsh", "terminal.integrated.defaultProfile.windows": "<PERSON><PERSON>", "terminal.integrated.cursorStyle": "line", "terminal.integrated.cursorBlinking": true, "terminal.integrated.fontSize": 16, "terminal.integrated.showExitAlert": false, "terminal.integrated.enablePersistentSessions": false, "editor.formatOnSave": true, "editor.tabSize": 2, "editor.fontSize": 14, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll.eslint": "explicit"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "files.exclude": {"**/out/**": true}, "search.exclude": {"**/out/**": true}, "files.watcherExclude": {"**/out/**": true}}