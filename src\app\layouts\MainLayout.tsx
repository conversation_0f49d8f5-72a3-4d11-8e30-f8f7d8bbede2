import { AppShell } from "@mantine/core";
import { useHotkeys } from "@mantine/hooks";
import { Outlet, useRouterState } from "@tanstack/react-router";
import { Header } from "app/components/Header/Header";
import { vars } from "app/styles/vars.css";
import { layout } from "./MainLayout.css";

export function MainLayout() {
  const { navbar, footer } = useRouterState({
    select: ({ matches }) => ({
      navbar: matches.find(({ context }) => context.navbar)?.context.navbar ?? null,
      footer: matches.find(({ context }) => context.footer)?.context.footer ?? null,
    }),
  });

  useHotkeys([["mod + R", () => window.electronWindow.forceReload()]]);

  return (
    <AppShell
      withBorder={true}
      className={layout}
      transitionDuration={0}
      header={{ height: vars.headerHeight }}
      footer={footer ? { height: vars.footerHeight } : undefined}
      navbar={navbar ? { width: vars.navbarWidth, breakpoint: 0 } : undefined}
    >
      <AppShell.Header>
        <Header />
      </AppShell.Header>
      {navbar && <AppShell.Navbar>{navbar}</AppShell.Navbar>}
      <AppShell.Main>
        <Outlet />
      </AppShell.Main>
      {footer && <AppShell.Footer>{footer}</AppShell.Footer>}
    </AppShell>
  );
}
