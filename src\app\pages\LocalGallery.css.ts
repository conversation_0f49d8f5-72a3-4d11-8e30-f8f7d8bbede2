import { style } from "@vanilla-extract/css";
import { calc } from "@vanilla-extract/css-utils";
import { vars } from "app/styles/vars.css";
import { mantineVars } from "app/theme";

export const content = style({
  display: "flex",
  justifyContent: "center",
});

export const viewport = style({
  height: calc.subtract("100dvh", vars.headerHeight),
  paddingBlock: mantineVars.spacing.md,
  paddingInline: mantineVars.spacing.xl,
});

export const simpleGrid = style({
  columnGap: mantineVars.spacing.md,
  rowGap: mantineVars.spacing.md,
  maxWidth: "1400px",
  width: "100%",
});

export const loading = style({
  display: "flex",
  gridColumn: "1 / -1",
  justifyContent: "center",
});
