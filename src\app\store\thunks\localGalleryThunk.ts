import type { ActionReducerMapBuilder } from "@reduxjs/toolkit";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { FetchStatus } from "shared/constants";
import type { AppDispatch, RootState } from "shared/types";
import type {
  LocalGalleryState,
  LocalImageFindManyOptions,
  LocalImageFindManyResponse,
} from "shared/types/localImageTypes";

type Builder = ActionReducerMapBuilder<LocalGalleryState>;

const thunk = createAsyncThunk.withTypes<{ state: RootState; dispatch: AppDispatch }>();

export const fetchManyThunk = thunk<LocalImageFindManyResponse, LocalImageFindManyOptions>(
  "localGallery/fetchMany",
  async (options) => window.electronLocalImages.findMany({ page: options.page })
);

export const fetchManyExtraReducers = (builder: Builder) => {
  builder.addCase(fetchManyThunk.pending, (state) => {
    state.fetchMany.status = FetchStatus.LOADING;
    state.fetchMany.error = null;
  });
  builder.addCase(fetchManyThunk.fulfilled, (state, { payload }) => {
    state.fetchMany.status = FetchStatus.SUCCEEDED;
    state.fetchMany.error = null;

    if (payload.page > 1) {
      state.fetchMany.data.push(...payload.data);
    } else {
      state.fetchMany.data = payload.data;
    }

    state.fetchMany.total = payload.total;
    state.fetchMany.limit = payload.limit;
    state.fetchMany.page = payload.page;
    state.fetchMany.hasNextPage = payload.hasNextPage;
  });
  builder.addCase(fetchManyThunk.rejected, (state, { error }) => {
    state.fetchMany.status = FetchStatus.FAILED;
    state.fetchMany.error = error;
  });
};
