import { vanillaExtractPlugin } from "@vanilla-extract/vite-plugin";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [
    vanillaExtractPlugin(),
    {
      name: "inline-config",
      apply(config, { mode }) {
        return mode === "development";
      },
      transformIndexHtml() {
        return [
          {
            tag: "script",
            children: "window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = { isDisabled: true }",
            injectTo: "body",
          },
        ];
      },
    },
  ],
  optimizeDeps: {
    include: ["@mantine/core", "@mantine/hooks", "@mantine/vanilla-extract"],
  },
  resolve: {
    alias: {
      src: "/src",
      shared: "/src/shared",
      app: "/src/app",
    },
  },
});
