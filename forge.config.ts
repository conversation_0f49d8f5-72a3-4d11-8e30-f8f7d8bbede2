import { MakerSquirrel } from "@electron-forge/maker-squirrel";
import { MakerZIP } from "@electron-forge/maker-zip";
import { FusesPlugin } from "@electron-forge/plugin-fuses";
import { VitePlugin } from "@electron-forge/plugin-vite";
import type { ForgeConfig } from "@electron-forge/shared-types";
import { FuseV1Options, FuseVersion } from "@electron/fuses";
import { cp, mkdir } from "node:fs/promises";
import { join } from "node:path";

const externalModules = ["@libsql", "libsql", "drizzle-orm", "js-base64", "@neon-rs/load", "ws", "promise-limit"];

const config: ForgeConfig = {
  packagerConfig: { asar: true },
  rebuildConfig: {},
  makers: [new MakerSquirrel({}), new MakerZIP({}, ["darwin"])],
  plugins: [
    new VitePlugin({
      build: [
        { entry: "src/main/index.ts", config: "vite.main.config.ts", target: "main" },
        { entry: "src/preload/preload.ts", config: "vite.preload.config.ts", target: "preload" },
      ],
      renderer: [{ name: "main_window", config: "vite.renderer.config.ts" }],
    }),
    // Fuses are used to enable/disable various Electron functionality
    // at package time, before code signing the application
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: true,
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
      [FuseV1Options.OnlyLoadAppFromAsar]: false,
    }),
  ],
  hooks: {
    postPackage: async (forgeConfig, options) => {
      const appPath = options.outputPaths[0];
      const nodeModulesPath = join(appPath, "resources", "node_modules");

      // Ensure node_modules directory exists
      await mkdir(nodeModulesPath, { recursive: true });

      // Copy required modules to the app's node_modules
      for (const name of externalModules) {
        await cp(join(process.cwd(), "node_modules", name), join(nodeModulesPath, name), { recursive: true });
      }

      console.log("Successfully copied external modules to packaged app");
    },
  },
};

export default config;
