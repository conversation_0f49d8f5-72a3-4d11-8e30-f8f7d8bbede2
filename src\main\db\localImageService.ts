import { count, desc, inArray } from "drizzle-orm";
import type { Dirent } from "node:fs";
import fs from "node:fs/promises";
import path from "node:path";
import { IMAGE_EXTENSIONS, PAGE_SIZE, SOURCE_IMAGES_DIR, THUMBNAILS_DIR } from "shared/constants";
import type {
  LocalImageFindManyOptions,
  LocalImageFindManyResponse,
  LocalImageSyncResponse,
} from "shared/types/localImageTypes";
import { db, LocalImagesTable } from ".";

export class LocalImageService {
  public async fetchMany(options: LocalImageFindManyOptions): Promise<LocalImageFindManyResponse> {
    const offset = (options.page - 1) * PAGE_SIZE;

    const [result, totalResult] = await Promise.all([
      db.select().from(LocalImagesTable).orderBy(desc(LocalImagesTable.createdAt)).limit(PAGE_SIZE).offset(offset),
      db.select({ count: count() }).from(LocalImagesTable),
    ]);

    const total = totalResult[0].count;
    const hasNextPage = offset + PAGE_SIZE < total;

    return {
      data: result,
      total,
      page: options.page,
      limit: PAGE_SIZE,
      hasNextPage,
    };
  }

  // Synchronize DB with files under SOURCE_IMAGES_DIR. Creates missing rows and
  // deletes rows whose files are gone. Thumbnail generation is stubbed for now.
  public async sync(): Promise<LocalImageSyncResponse> {
    const errors: string[] = [];

    console.log(`[LocalImageService.sync] Starting sync. SOURCE_IMAGES_DIR=${SOURCE_IMAGES_DIR}`);

    if (!SOURCE_IMAGES_DIR || typeof SOURCE_IMAGES_DIR !== "string") {
      console.error("[LocalImageService.sync] SOURCE_IMAGES_DIR is not configured.");
      return { scanned: 0, added: 0, removed: 0, skipped: 0, errors: ["SOURCE_IMAGES_DIR is not configured."] };
    }

    // Ensure the directory exists and is readable
    try {
      const stat = await fs.stat(SOURCE_IMAGES_DIR);
      if (!stat.isDirectory()) {
        console.error("[LocalImageService.sync] SOURCE_IMAGES_DIR is not a directory.");
        return { scanned: 0, added: 0, removed: 0, skipped: 0, errors: ["SOURCE_IMAGES_DIR is not a directory."] };
      }
    } catch (error) {
      const message = `SOURCE_IMAGES_DIR not accessible: ${String((error as Error).message || error)}`;
      console.error(`[LocalImageService.sync] ${message}`);
      return { scanned: 0, added: 0, removed: 0, skipped: 0, errors: [message] };
    }
    console.log(`[LocalImageService.sync] Source directory validated: ${SOURCE_IMAGES_DIR}`);

    // Read image file paths only (non-recursive)
    const imageFilePaths = await this.walkDir(SOURCE_IMAGES_DIR, errors);
    console.log(`[LocalImageService.sync] Image files discovered: ${imageFilePaths.length}`);

    // Build maps for comparison (case-insensitive on Windows)
    const normalizePathForCompare = (filePath: string) => this.normalizeForCompare(filePath);

    const filesystemPathSet = new Set(imageFilePaths.map(normalizePathForCompare));

    // Fetch minimal data from DB to compare
    const databaseRows = await db
      .select({ id: LocalImagesTable.id, filePath: LocalImagesTable.filePath })
      .from(LocalImagesTable);

    const databaseRowsByNormalizedPath = new Map<string, { id: number; filePath: string }[]>();
    for (const row of databaseRows) {
      const key = normalizePathForCompare(row.filePath);
      const records = databaseRowsByNormalizedPath.get(key);
      if (records) records.push(row);
      else databaseRowsByNormalizedPath.set(key, [row]);
    }

    // Determine which to add and which to remove
    const filePathsToAdd = imageFilePaths.filter(
      (filePath) => !databaseRowsByNormalizedPath.has(normalizePathForCompare(filePath))
    );

    const idsToRemove: number[] = [];
    let duplicatesRemovedCount = 0;
    for (const [normalizedPath, records] of databaseRowsByNormalizedPath.entries()) {
      if (!filesystemPathSet.has(normalizedPath)) {
        // File no longer exists -> remove all rows for it
        for (const row of records) idsToRemove.push(row.id);
      } else if (records.length > 1) {
        // Deduplicate accidental duplicates in DB, keep the first
        duplicatesRemovedCount += records.length - 1;
        for (const row of records.slice(1)) idsToRemove.push(row.id);
      }
    }

    console.log(
      `[LocalImageService.sync] Planned changes: toAdd=${filePathsToAdd.length}, toRemove=${idsToRemove.length} (duplicates=${duplicatesRemovedCount})`
    );

    let removedCount = 0;
    if (idsToRemove.length) {
      // Prefer a single DELETE ... WHERE id IN (...) for performance
      await db.delete(LocalImagesTable).where(inArray(LocalImagesTable.id, idsToRemove));
      removedCount = idsToRemove.length;
      console.log(`[LocalImageService.sync] Removed ${removedCount} records.`);
    }

    let addedCount = 0;
    if (filePathsToAdd.length) {
      const insertValues = await Promise.all(
        filePathsToAdd.map(async (absolutePath) => {
          try {
            const fileStat = await fs.stat(absolutePath);
            const fileName = path.basename(absolutePath);
            const thumbnailPath = await this.ensureThumbnailPlaceholder(fileName);
            // Width/height unknown without native deps; use 0 for now. isPortrait false by default.
            return {
              filePath: path.normalize(absolutePath),
              fileName,
              fileSize: fileStat.size,
              thumbnailPath,
              width: 0,
              height: 0,
              isPortrait: false,
              isArchived: false,
            } as const;
          } catch (e) {
            const message = `Failed to stat file ${absolutePath}: ${String((e as Error).message || e)}`;
            console.error(`[LocalImageService.sync] ${message}`);
            errors.push(message);
            return null;
          }
        })
      );

      const rowsToInsert = insertValues.filter((v): v is NonNullable<typeof v> => v !== null);
      if (rowsToInsert.length) {
        await db.insert(LocalImagesTable).values(rowsToInsert);
        addedCount = rowsToInsert.length;
        console.log(`[LocalImageService.sync] Inserted ${addedCount} records.`);
      }
    }

    const result: LocalImageSyncResponse = {
      added: addedCount,
      scanned: imageFilePaths.length,
      removed: removedCount,
      skipped: imageFilePaths.length - addedCount,
      errors,
    };

    console.log(
      `[LocalImageService.sync] Completed. scanned=${result.scanned} added=${result.added} removed=${result.removed} skipped=${result.skipped} errors=${result.errors.length}`
    );

    return result;
  }

  // Read a single directory (non-recursive) and return absolute file paths of images only.
  private async walkDir(rootDirectory: string, errors: string[]): Promise<string[]> {
    console.log(`[LocalImageService.walkDir] Reading directory: ${rootDirectory}`);
    const entries: Dirent[] = [];
    try {
      entries.push(...(await fs.readdir(rootDirectory, { withFileTypes: true })));
    } catch (e) {
      const message = `Failed to read directory ${rootDirectory}: ${String((e as Error).message || e)}`;
      console.error(`[LocalImageService.walkDir] ${message}`);
      errors.push(message);
      return [];
    }

    const imageFilePaths: string[] = [];
    for (const entry of entries) {
      const fullPath = path.join(rootDirectory, entry.name);
      if (entry.isDirectory()) {
        // No recursion: ignore directories present in the source directory
        console.log(`[LocalImageService.walkDir] Ignoring directory: ${fullPath}`);
        continue;
      }
      if (entry.isFile()) {
        if (this.isImage(fullPath)) {
          imageFilePaths.push(fullPath);
        } else {
          console.log(`[LocalImageService.walkDir] Ignoring non-image file: ${fullPath}`);
        }
      }
    }

    console.log(`[LocalImageService.walkDir] Found ${imageFilePaths.length} image files in ${rootDirectory}`);

    return imageFilePaths;
  }

  private isImage(filepath: string): boolean {
    const extension = path.extname(filepath).toLowerCase();

    return IMAGE_EXTENSIONS.has(extension);
  }

  private normalizeForCompare(filepath: string): string {
    const normalizedPath = path.normalize(filepath);
    return process.platform === "win32" ? normalizedPath.toLowerCase() : normalizedPath;
  }

  // Create a thumbnails path placeholder; actual generation is a future feature.
  private async ensureThumbnailPlaceholder(fileName: string): Promise<string> {
    const baseThumbsDir = THUMBNAILS_DIR || path.join(SOURCE_IMAGES_DIR || "", ".thumbnails");
    if (baseThumbsDir) {
      try {
        await fs.mkdir(baseThumbsDir, { recursive: true });
      } catch {
        // ignore mkdir errors; we'll still return a path
      }
    }
    // Use a deterministic placeholder filename; do not create the file yet.
    const bare = fileName.replace(/\.[^.]+$/, "");
    return path.normalize(path.join(baseThumbsDir || "", `${bare}.thumb.jpg`));
  }
}
