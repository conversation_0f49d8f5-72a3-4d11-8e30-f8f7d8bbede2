import { net, protocol, type BrowserWindow } from "electron";
import { registerDevtoolsHandlers } from "./devtoolsHandlers";
import { registerLocalImageHandlers } from "./localImageHandlers";
import { registerWindowHandlers } from "./windowHandlers";

export function registerAllHandlers(mainWindow: BrowserWindow) {
  registerWindowHandlers(mainWindow);
  registerDevtoolsHandlers(mainWindow);
  registerLocalImageHandlers();

  // Handle requests for app:// URLs
  protocol.handle("app", async (request) => await net.fetch(request.url.replace("app:///", "file:///")));
}
