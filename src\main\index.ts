import { app, BrowserWindow, Menu } from "electron";
import started from "electron-squirrel-startup";
import { join } from "node:path";
import { registerAllHandlers } from "./ipc";

if (started) {
  app.quit();
}

const createWindow = () => {
  const mainWindow = new BrowserWindow({
    show: false,
    width: 1200,
    height: 800,
    minWidth: 600,
    minHeight: 120,
    titleBarStyle: "hidden",
    backgroundColor: "#242424",
    icon: "src/assets/logo.png",
    webPreferences: { preload: join(__dirname, "preload.js") },
  });

  mainWindow.once("ready-to-show", () => mainWindow.show());

  registerAllHandlers(mainWindow);
  Menu.setApplicationMenu(null);

  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`));
  }
};

app.whenReady().then(createWindow);

// macOS only
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

// macOS only
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
