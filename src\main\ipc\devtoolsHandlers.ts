import { ipc<PERSON><PERSON>, type BrowserWindow } from "electron";
import { DEVTOOLS } from "shared/constants/ipc-channels";

export function registerDevtoolsHandlers(window: BrowserWindow) {
  ipcMain.handle(DEVTOOLS.TOGGLE, () =>
    window.webContents.isDevToolsOpened() ? window.webContents.closeDevTools() : window.webContents.openDevTools()
  );

  window.webContents.on("devtools-opened", () => window.webContents.send(DEVTOOLS.OPEN));
  window.webContents.on("devtools-closed", () => window.webContents.send(DEVTOOLS.CLOSE));
}
