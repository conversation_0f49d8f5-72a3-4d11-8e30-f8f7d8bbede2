import { globalStyle, style } from "@vanilla-extract/css";
import { mantineVars } from "src/app/theme";

const itemLabel = style({
  color: mantineVars.colors.text,
  fontSize: mantineVars.fontSizes.sm,
  letterSpacing: "0.01rem",
});

const itemSection = style({});

globalStyle(`${itemSection} > svg`, {
  color: mantineVars.colors.text,
  transform: "scale(0.8)",
  strokeWidth: 0.8,
});

globalStyle(`${itemSection} > p`, {
  color: mantineVars.colors.dimmed,
  fontSize: mantineVars.fontSizes.sm,
  letterSpacing: "0.05rem",
});

export { itemLabel, itemSection };
