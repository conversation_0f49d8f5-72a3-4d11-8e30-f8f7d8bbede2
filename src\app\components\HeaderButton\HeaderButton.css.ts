import { createVar, fallbackVar, style } from "@vanilla-extract/css";
import { calc } from "@vanilla-extract/css-utils";
import { vars } from "src/app/styles/vars.css";
import { mantineVars } from "src/app/theme";

const bgColorHover = createVar();

const headerButton = style({
  alignItems: "center",
  display: "flex",
  justifyContent: "center",
  height: "100%",
  width: calc.add(vars.headerHeight, "0.5rem"),

  ":hover": { backgroundColor: fallbackVar(bgColorHover, vars.bgColorPrimary) },
});

const headerIcon = style({
  color: mantineVars.colors.white,
  strokeWidth: 0.8,
});

export { bgColorHover, headerButton, headerIcon };
