import { ipc<PERSON><PERSON><PERSON> } from "electron";
import { DEVTOOLS } from "shared/constants/ipc-channels";

export const devtoolsContext = {
  toggle: (): Promise<void> => ipcRenderer.invoke(DEVTOOLS.TOGGLE),

  onOpened: (callback: () => void) => ipcRenderer.on(DEVTOOLS.OPEN, callback),
  onClosed: (callback: () => void) => ipcRenderer.on(DEVTOOLS.CLOSE, callback),

  removeListeners: () => {
    ipcRenderer.removeAllListeners(DEVTOOLS.OPEN);
    ipcRenderer.removeAllListeners(DEVTOOLS.CLOSE);
  },
};
