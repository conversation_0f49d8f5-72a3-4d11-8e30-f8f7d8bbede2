import { createSlice } from "@reduxjs/toolkit";
import { fetchManyExtraReducers } from "app/store/thunks/localGalleryThunk";
import { FetchStatus, PAGE_SIZE } from "shared/constants";
import type { RootState } from "shared/types";
import type { LocalGalleryState } from "shared/types/localImageTypes";

const localGalleryInitialState: LocalGalleryState = {
  fetchMany: {
    status: FetchStatus.IDLE,
    error: null,
    data: [],
    total: 0,
    limit: PAGE_SIZE,
    page: 0,
    hasNextPage: false,
  },
};

const localGallerySlice = createSlice({
  name: "localGallery",
  initialState: localGalleryInitialState,
  reducers: {},
  extraReducers: (builder) => {
    fetchManyExtraReducers(builder);
  },
});

// TODO Add actions
// export const { } = localGallerySlice.actions;

export const selectLocalGallery = (state: RootState) => state.localGallery;

export const localGalleryReducer = localGallerySlice.reducer;
