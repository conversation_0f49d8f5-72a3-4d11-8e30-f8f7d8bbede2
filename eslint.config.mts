import js from "@eslint/js";
import importPlugin from "eslint-plugin-import";
import perfectionist from "eslint-plugin-perfectionist";
import { defineConfig } from "eslint/config";
import globals from "globals";
import tseslint from "typescript-eslint";

const perfectionistConfig = {
  order: "asc",
  type: "natural",
  ignoreCase: true,
  groups: [
    "layout",
    "positioning",
    "box-model",
    "typography",
    "animation",
    "visual",
    { newlinesBetween: 1 },
    "webkit",
    "pseudo-selectors",
    "at-rules",
    { newlinesBetween: 1 },
    "unknown",
  ],
  customGroups: [
    {
      groupName: "layout",
      selector: "property",
      elementNamePattern: "^(display|flex.*|grid.*|align.*|justify.*|place.*|gap|.*Gap|zIndex)$",
    },
    {
      groupName: "positioning",
      selector: "property",
      elementNamePattern: "^(position|top|right|bottom|left|inset.*)$",
    },
    {
      groupName: "box-model",
      selector: "property",
      elementNamePattern: "^(width|height|min.*|max.*|margin.*|padding.*|border.*|outline.*|overflow.*)$",
    },
    {
      groupName: "typography",
      selector: "property",
      elementNamePattern: "^(font.*|fontSize|lineHeight|letterSpacing|text.*|color)$",
    },
    {
      groupName: "animation",
      selector: "property",
      elementNamePattern: "^(transition.*|animation.*|transform.*)$",
    },
    {
      groupName: "visual",
      selector: "property",
      elementNamePattern: "^(background.*|opacity|visibility|filter|backdropFilter|.*Shadow|pointer.*|stroke.*)$",
    },
    { groupName: "webkit", selector: "property", elementNamePattern: "^-" },
    { groupName: "pseudo-selectors", selector: "property", elementNamePattern: "^[:&]" },
    { groupName: "at-rules", selector: "property", elementNamePattern: "^@" },
  ],
};

export default defineConfig([
  { ignores: [".vite/**", "out/**", ".drizzle/**", "node_modules/**"] },
  {
    files: ["**/*.{js,mjs,ts,mts,cts,jsx,tsx}"],
    ...js.configs.recommended,
    languageOptions: { globals: globals.browser },
  },
  {
    files: ["**/*.cjs"],
    ...js.configs.recommended,
    languageOptions: { globals: globals.node },
  },
  {
    files: ["src/**/*.{ts,tsx}"],
    ...importPlugin.flatConfigs.recommended,
    ...importPlugin.flatConfigs.typescript,
    languageOptions: { ecmaVersion: "latest", sourceType: "module" },
    rules: {
      "import/no-default-export": "error",
      "@typescript-eslint/consistent-type-imports": ["error", { disallowTypeAnnotations: false }],
    },
    settings: { "import/resolver": { typescript: {} } },
  },
  ...tseslint.configs.strict,
  {
    files: ["**/*.css.ts"],
    ignores: ["src/app/styles/vars.css.ts"],
    plugins: { perfectionist },
    rules: { "perfectionist/sort-objects": ["error", perfectionistConfig] },
  },
]);
