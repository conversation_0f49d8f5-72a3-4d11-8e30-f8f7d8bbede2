import { Menu, Switch, Text } from "@mantine/core";
import { HeaderButton } from "app/components/HeaderButton/HeaderButton";
import { useDevtools } from "app/hooks";
import { PictureInPicture, RotateCw, Wrench } from "lucide-react";
import { useState } from "react";
import { itemLabel, itemSection } from "./HeaderMenu.css";

export function HeaderMenu() {
  const [checkedAlwaysOnTop, setCheckedAlwaysOnTop] = useState(false);

  const { state, loading, toggle } = useDevtools();

  return (
    <Menu withArrow arrowSize={12} trigger="hover" withinPortal={false} classNames={{ itemLabel, itemSection }}>
      <Menu.Target>
        <HeaderButton icon="menu" />
      </Menu.Target>
      <Menu.Dropdown>
        <Menu.Item
          closeMenuOnClick={false}
          leftSection={<PictureInPicture />}
          rightSection={<Switch checked={checkedAlwaysOnTop} />}
          onClick={() => setCheckedAlwaysOnTop(!checkedAlwaysOnTop)}
        >
          Always On Top
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item
          leftSection={<RotateCw />}
          rightSection={<Text>Ctrl+R</Text>}
          onClick={() => window.electronWindow.forceReload()}
        >
          Force Reload
        </Menu.Item>
        <Menu.Item
          closeMenuOnClick={false}
          leftSection={<Wrench />}
          rightSection={<Switch checked={state} disabled={loading} />}
          onClick={toggle}
          disabled={loading}
        >
          Toggle Developer Tools
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
}
