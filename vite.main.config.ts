import { defineConfig } from "vite";

export default defineConfig({
  resolve: {
    alias: {
      src: "/src",
      shared: "/src/shared",
    },
  },
  build: {
    commonjsOptions: {
      dynamicRequireTargets: ["@libsql/win32-x64-msvc"],
    },
    rollupOptions: {
      external: [
        "@libsql/client",
        "@libsql/win32-x64-msvc",
        "@libsql/darwin-arm64",
        "@libsql/darwin-x64",
        "@libsql/linux-x64-gnu",
        "@libsql/linux-arm64-gnu",
        "drizzle-orm",
        "drizzle-orm/libsql",
      ],
      output: {
        format: "cjs",
      },
    },
  },
});
