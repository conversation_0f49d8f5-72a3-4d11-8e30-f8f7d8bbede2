export {};

declare global {
  interface Window {
    electronWindow: typeof import("src/preload/windowContext").windowContext;
    electronDevtools: typeof import("src/preload/devtoolsContext").devtoolsContext;
    electronLocalImages: typeof import("src/preload/localImageContext").localImageContext;
  }
}

declare module "csstype" {
  interface Properties {
    "-webkit-app-region"?: "drag" | "no-drag";
  }
}
