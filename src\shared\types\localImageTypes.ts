import type { SerializedError } from "@reduxjs/toolkit";
import type { FetchStatus } from "shared/constants";
import type { LocalImage } from "src/main/db/schema";

export { LocalImage } from "src/main/db/schema";

export interface LocalImageFindManyOptions {
  page: number;
}

export interface LocalImageFindManyResponse {
  data: LocalImage[];
  total: number;
  limit: number;
  page: number;
  hasNextPage: boolean;
}

export interface LocalImageSyncResponse {
  added: number;
  scanned: number;
  removed: number;
  skipped: number;
  errors: string[];
}

export interface LocalGalleryState {
  fetchMany: {
    status: FetchStatus;
    error: SerializedError | null;
    data: LocalImage[];
    total: number;
    limit: number;
    page: number;
    hasNextPage: boolean;
  };
}
