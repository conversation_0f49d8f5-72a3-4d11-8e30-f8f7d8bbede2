{"name": "viper-app", "productName": "viper-app", "version": "0.0.1", "description": "Viper App", "main": ".vite/build/index.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "lint": "eslint --fix .", "format": "prettier --write ."}, "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.3", "@electron-forge/maker-squirrel": "^7.8.3", "@electron-forge/maker-zip": "^7.8.3", "@electron-forge/plugin-auto-unpack-natives": "^7.8.3", "@electron-forge/plugin-fuses": "^7.8.3", "@electron-forge/plugin-vite": "^7.8.3", "@electron/fuses": "^1.8.0", "@types/electron-squirrel-startup": "^1.0.2", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@types/redux-logger": "^3.0.13", "@vanilla-extract/vite-plugin": "^5.1.1", "drizzle-kit": "^0.31.4", "electron": "38.0.0", "eslint": "^9.34.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-perfectionist": "^4.15.0", "globals": "^16.3.0", "postcss": "^8.5.6", "postcss-preset-mantine": "^1.18.0", "postcss-simple-vars": "^7.0.1", "prettier": "3.6.2", "typescript": "^5.9.2", "typescript-eslint": "^8.42.0", "vite": "^7.1.4"}, "dependencies": {"@libsql/client": "^0.15.15", "@mantine/core": "^8.2.8", "@mantine/hooks": "^8.2.8", "@mantine/vanilla-extract": "^8.2.8", "@reduxjs/toolkit": "^2.9.0", "@tanstack/react-router": "^1.131.35", "@vanilla-extract/css": "^1.17.4", "@vanilla-extract/css-utils": "^0.1.6", "@vanilla-extract/dynamic": "^2.1.5", "drizzle-orm": "^0.44.5", "electron-squirrel-startup": "^1.0.1", "electron-store": "^10.1.0", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-infinite-scroll-hook": "^6.0.1", "react-redux": "^9.2.0", "redux-logger": "^3.0.6", "serialize-error": "^12.0.0"}}